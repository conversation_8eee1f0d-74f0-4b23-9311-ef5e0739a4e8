"use client"

import Link from "next/link"
import Image from "next/image"
import { useState, useEffect } from "react"
import { User, Menu, X, Wallet, Shield, ChevronDown, Search, Settings } from "lucide-react"
import MobileSidebar from "./MobileSidebar"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // TODO: Replace with actual user data from Supabase
  const mockUser = {
    name: "أحمد محمد",
    walletBalance: 150.0,
    isLoggedIn: true,
    role: "admin", // Change this to test different roles
  }

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen && !(event.target as Element).closest('.mobile-menu-container')) {
        setIsMenuOpen(false)
      }
    }
    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isMenuOpen])

  // Navigation items - conditionally include admin dashboard for admin users
  const navItems = [
    { href: "/", label: "الرئيسية", icon: null },
    { href: "/shop", label: "المتجر", icon: null },
    { href: "/wallet", label: "المحفظة", icon: null },
    { href: "/profile", label: "الملف الشخصي", icon: null },
    // Show admin dashboard only for admin users
    ...(mockUser.role === "admin" ? [{ href: "/admin", label: "لوحة التحكم", icon: Settings }] : []),
  ]

  return (
    <>
      {/* Mobile Sidebar */}
      <MobileSidebar isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      <header
        className="bg-gray-900/95 backdrop-blur-md border-b border-gray-800/50 sticky top-0 z-30"
        role="banner"
      >
        {/* Desktop Header */}
        <div className="hidden lg:block">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Logo */}
              <Link
                href="/"
                className="flex items-center space-x-3 space-x-reverse group"
                aria-label="بنتاكون - الصفحة الرئيسية"
              >
                <div className="w-8 h-8 rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-shadow duration-200">
                  <Image
                    src="/logo.jpg"
                    alt="بنتاكون"
                    width={32}
                    height={32}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-xl font-bold text-white">بنتاكون</span>
              </Link>

              {/* Navigation Links */}
              <nav className="hidden xl:flex items-center space-x-8 space-x-reverse">
                {navItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className="flex items-center space-x-2 space-x-reverse text-gray-300 hover:text-white transition-colors duration-200 group"
                    >
                      {Icon && <Icon className="w-4 h-4 group-hover:text-purple-400 transition-colors duration-200" />}
                      <span className="text-sm font-medium">{item.label}</span>
                    </Link>
                  )
                })}
              </nav>

              {/* Search Bar - Center */}
              <div className="flex-1 max-w-md mx-8">
                <div className="relative w-full">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="ابحث عن الألعاب أو المنتجات"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-sm text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200"
                  />
                </div>
              </div>

              {/* Right Side - Login */}
              <div className="flex items-center">
                {/* Login Button */}
                <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg">
                  تسجيل الدخول
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Header */}
        <div className="lg:hidden">
          <div className="px-4 py-2 space-y-2">
            {/* Top Row: Logo and Login Button */}
            <div className="flex items-center justify-between">
              <Link
                href="/"
                className="flex items-center space-x-2 space-x-reverse"
                aria-label="بنتاكون - الصفحة الرئيسية"
              >
                <div className="w-8 h-8 rounded-lg overflow-hidden shadow-md">
                  <Image
                    src="/logo.jpg"
                    alt="بنتاكون"
                    width={32}
                    height={32}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-lg font-bold text-white">بنتاكون</span>
              </Link>

              {/* Login Button */}
              <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg">
                تسجيل الدخول
              </button>
            </div>

            {/* Second Row: Menu Button and Search Bar */}
            <div className="flex items-center space-x-3 space-x-reverse">
              {/* Menu Button */}
              <button
                className="flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 flex-shrink-0"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                aria-label={isMenuOpen ? "إغلاق القائمة" : "فتح القائمة"}
              >
                <Menu className="w-5 h-5 text-gray-300" />
              </button>

              {/* Search Bar */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="ابحث عن الألعاب أو المنتجات"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-sm text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>
      </header>

    </>
  )
}
