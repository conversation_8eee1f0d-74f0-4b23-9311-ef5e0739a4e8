"use client"

import { useState, useMemo } from "react"
import { Search, Filter, Eye, Check, X, Calendar, DollarSign, User, Package, Key, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import type { Order } from "../../types"
import { convertAndFormatPrice } from "../../utils/currency"
import { useData } from "../../contexts/DataContext"

export default function OrderManagement() {
  const [orders, setOrders] = useState(mockOrders)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "pending" | "completed" | "failed">("all")
  const [dateFilter, setDateFilter] = useState<"all" | "today" | "week" | "month">("all")

  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Filter and search orders
  const filteredOrders = useMemo(() => {
    let filtered = orders

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter((order) => {
        const user = mockUsers.find(u => u.id === order.userId)
        const product = mockProducts.find(p => p.id === order.productId)
        const searchLower = searchTerm.toLowerCase()
        
        return (
          order.id.toLowerCase().includes(searchLower) ||
          user?.name.toLowerCase().includes(searchLower) ||
          user?.email.toLowerCase().includes(searchLower) ||
          product?.title.toLowerCase().includes(searchLower)
        )
      })
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Date filter
    if (dateFilter !== "all") {
      const now = new Date()

      filtered = filtered.filter(order => {
        const orderDate = new Date(order.createdAt)
        
        switch (dateFilter) {
          case "today":
            return orderDate.toDateString() === now.toDateString()
          case "week":
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            return orderDate >= weekAgo
          case "month":
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
            return orderDate >= monthAgo
          default:
            return true
        }
      })
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }, [orders, searchTerm, statusFilter, dateFilter])

  const handleOrderAction = async (orderId: string, action: "approve" | "decline") => {
    setIsLoading(true)
    
    // TODO: Implement with Supabase
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newStatus = action === "approve" ? "completed" : "failed"
    setOrders(prev => prev.map(order => 
      order.id === orderId ? { ...order, status: newStatus as any } : order
    ))
    
    setIsLoading(false)
  }

  const openDetailModal = (order: Order) => {
    setSelectedOrder(order)
    setIsDetailModalOpen(true)
  }

  const closeDetailModal = () => {
    setIsDetailModalOpen(false)
    setSelectedOrder(null)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-400" />
      case "failed":
        return <XCircle className="w-4 h-4 text-red-400" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-400/10 text-green-400"
      case "pending":
        return "bg-yellow-400/10 text-yellow-400"
      case "failed":
        return "bg-red-400/10 text-red-400"
      default:
        return "bg-gray-400/10 text-gray-400"
    }
  }

  const getCardBorderColor = (status: string) => {
    switch (status) {
      case "completed":
        return "border-green-500/50 bg-green-900/10"
      case "pending":
        return "border-yellow-500/50 bg-yellow-900/10"
      case "failed":
        return "border-red-500/50 bg-red-900/10"
      default:
        return "border-gray-600/50 bg-gray-700/30"
    }
  }

  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === "pending").length,
    completed: orders.filter(o => o.status === "completed").length,
    failed: orders.filter(o => o.status === "failed").length,
    totalRevenue: orders.filter(o => o.status === "completed").reduce((sum, o) => sum + o.amount, 0)
  }

  return (
    <div className="space-y-6">
      <div className="responsive-header">
        <h2 className="responsive-title">إدارة الطلبات</h2>
        <div className="text-sm text-gray-400">{filteredOrders.length} من {orders.length} طلب</div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-xs md:text-sm">إجمالي الطلبات</p>
              <p className="text-xl md:text-2xl font-bold">{stats.total}</p>
            </div>
            <Package className="w-6 h-6 md:w-8 md:h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-xs md:text-sm">قيد الانتظار</p>
              <p className="text-xl md:text-2xl font-bold text-yellow-400">{stats.pending}</p>
            </div>
            <Clock className="w-6 h-6 md:w-8 md:h-8 text-yellow-400" />
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-xs md:text-sm">مكتملة</p>
              <p className="text-xl md:text-2xl font-bold text-green-400">{stats.completed}</p>
            </div>
            <CheckCircle className="w-6 h-6 md:w-8 md:h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-xs md:text-sm">إجمالي الإيرادات</p>
              <p className="text-xl md:text-2xl font-bold text-purple-400">{convertAndFormatPrice(stats.totalRevenue)}</p>
            </div>
            <DollarSign className="w-6 h-6 md:w-8 md:h-8 text-purple-400" />
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        <div className="p-4 md:p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="البحث برقم الطلب، اسم المستخدم، أو المنتج..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-gray-700/50 border border-gray-600/50 rounded-xl px-10 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="w-full bg-gray-700/50 border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
              >
                <option value="all">جميع الحالات</option>
                <option value="pending">قيد الانتظار</option>
                <option value="completed">مكتملة</option>
                <option value="failed">فاشلة</option>
              </select>
            </div>

            {/* Date Filter */}
            <div>
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value as any)}
                className="w-full bg-gray-700/50 border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
              >
                <option value="all">جميع التواريخ</option>
                <option value="today">اليوم</option>
                <option value="week">آخر أسبوع</option>
                <option value="month">آخر شهر</option>
              </select>
            </div>


          </div>
        </div>
      </div>

      {/* Orders Display */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        <div className="p-4 md:p-6">
          {/* Desktop Table */}
          <div className="hidden lg:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-right text-gray-400 text-sm">
                  <th className="pb-3">رقم الطلب</th>
                  <th className="pb-3">المستخدم</th>
                  <th className="pb-3">المنتج</th>
                  <th className="pb-3">المبلغ</th>
                  <th className="pb-3">الحالة</th>
                  <th className="pb-3">كود رقمي</th>
                  <th className="pb-3">التاريخ</th>
                  <th className="pb-3">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredOrders.map((order) => {
                  const user = mockUsers.find(u => u.id === order.userId)
                  const product = mockProducts.find(p => p.id === order.productId)
                  const package_ = product?.packages.find(p => p.id === order.packageId)

                  return (
                    <tr key={order.id} className="border-t border-gray-700/50">
                      <td className="py-4">
                        <span className="font-mono text-sm">#{order.id}</span>
                      </td>
                      <td className="py-4">
                        <div>
                          <p className="font-semibold">{user?.name || "مستخدم غير معروف"}</p>
                          <p className="text-sm text-gray-400">{user?.email}</p>
                        </div>
                      </td>
                      <td className="py-4">
                        <div>
                          <p className="font-semibold">{product?.title || "منتج غير معروف"}</p>
                          <p className="text-sm text-gray-400">{package_?.name}</p>
                        </div>
                      </td>
                      <td className="py-4">
                        <span className="font-bold text-lg">{convertAndFormatPrice(order.amount)}</span>
                      </td>
                      <td className="py-4">
                        <div className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-xl text-xs ${getStatusColor(order.status)}`}>
                          {getStatusIcon(order.status)}
                          <span>
                            {order.status === "completed" ? "مكتمل" :
                             order.status === "pending" ? "قيد الانتظار" : "فاشل"}
                          </span>
                        </div>
                      </td>
                      <td className="py-4">
                        {order.digitalCode && order.status === "completed" ? (
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <Key className="w-4 h-4 text-blue-400" />
                            <span className="text-sm text-blue-400 font-mono">{order.digitalCode.key.substring(0, 8)}...</span>
                          </div>
                        ) : package_?.hasDigitalCodes ? (
                          <span className="text-xs text-gray-500">منتج رقمي</span>
                        ) : (
                          <span className="text-xs text-gray-500">-</span>
                        )}
                      </td>
                      <td className="py-4">
                        <span className="text-sm text-gray-400">
                          {new Date(order.createdAt).toLocaleDateString("ar")}
                        </span>
                      </td>
                      <td className="py-4">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => openDetailModal(order)}
                            className="p-2 text-gray-400 hover:text-blue-400 transition-colors rounded-xl hover:bg-blue-400/10"
                            title="عرض التفاصيل"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          {order.status === "pending" && (
                            <>
                              <button
                                onClick={() => handleOrderAction(order.id, "approve")}
                                className="p-2 text-gray-400 hover:text-green-400 transition-colors rounded-xl hover:bg-green-400/10"
                                disabled={isLoading}
                                title="موافقة"
                              >
                                <Check className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleOrderAction(order.id, "decline")}
                                className="p-2 text-gray-400 hover:text-red-400 transition-colors rounded-xl hover:bg-red-400/10"
                                disabled={isLoading}
                                title="رفض"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-4">
            {filteredOrders.map((order) => {
              const user = mockUsers.find(u => u.id === order.userId)
              const product = mockProducts.find(p => p.id === order.productId)
              const package_ = product?.packages.find(p => p.id === order.packageId)

              return (
                <div key={order.id} className={`backdrop-blur-sm rounded-xl p-4 border transition-all duration-300 hover:border-gray-500/50 ${getCardBorderColor(order.status)}`}>
                  {/* Order Header */}
                  <div className="flex items-center justify-between mb-3">
                    <span className="font-mono text-sm text-gray-400">#{order.id}</span>
                    <div className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-xl text-xs ${getStatusColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      <span>
                        {order.status === "completed" ? "مكتمل" :
                         order.status === "pending" ? "قيد الانتظار" : "فاشل"}
                      </span>
                    </div>
                  </div>

                  {/* Order Details */}
                  <div className="space-y-3 mb-4">
                    <div>
                      <p className="text-xs text-gray-400 mb-1">المستخدم</p>
                      <p className="font-semibold">{user?.name || "مستخدم غير معروف"}</p>
                      <p className="text-sm text-gray-400">{user?.email}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">المنتج</p>
                      <p className="font-semibold">{product?.title || "منتج غير معروف"}</p>
                      <p className="text-sm text-gray-400">{package_?.name}</p>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-xs text-gray-400 mb-1">المبلغ</p>
                        <p className="font-bold text-lg text-purple-400">{convertAndFormatPrice(order.amount)}</p>
                      </div>
                      <div className="text-left">
                        <p className="text-xs text-gray-400 mb-1">التاريخ</p>
                        <p className="text-sm">{new Date(order.createdAt).toLocaleDateString("ar")}</p>
                      </div>
                    </div>
                  </div>

                  {/* Digital Code Section for Completed Orders */}
                  {order.status === "completed" && order.digitalCode && (
                    <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-3 mb-4">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <Key className="w-4 h-4 text-blue-400" />
                        <span className="text-sm font-semibold text-blue-400">كود رقمي متاح</span>
                      </div>
                      <div className="font-mono text-sm bg-gray-900/50 rounded-lg p-2 border border-gray-600/50">
                        <span className="text-green-400 font-bold">{order.digitalCode.key}</span>
                      </div>
                      {order.digitalCode.revealed && (
                        <p className="text-xs text-blue-300 mt-2">
                          تم عرض الكود {order.digitalCode.viewCount} مرة
                          {order.digitalCode.revealedAt &&
                            ` • آخر عرض: ${new Date(order.digitalCode.revealedAt).toLocaleDateString("ar")}`}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className={order.status === "pending" ? "mobile-grid-actions-3" : "mobile-grid-actions"}>
                    <button
                      onClick={() => openDetailModal(order)}
                      className="mobile-button bg-blue-600/20 text-blue-400 hover:bg-blue-600/30"
                    >
                      <Eye className="w-4 h-4" />
                      <span>عرض</span>
                    </button>
                    {order.status === "pending" && (
                      <>
                        <button
                          onClick={() => handleOrderAction(order.id, "approve")}
                          className="mobile-button bg-green-600/20 text-green-400 hover:bg-green-600/30"
                          disabled={isLoading}
                        >
                          <Check className="w-4 h-4" />
                          <span>موافقة</span>
                        </button>
                        <button
                          onClick={() => handleOrderAction(order.id, "decline")}
                          className="mobile-button bg-red-600/20 text-red-400 hover:bg-red-600/30"
                          disabled={isLoading}
                        >
                          <X className="w-4 h-4" />
                          <span>رفض</span>
                        </button>
                      </>
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          {filteredOrders.length === 0 && (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-400 mb-2">لا توجد طلبات</h3>
              <p className="text-gray-500">لم يتم العثور على طلبات تطابق معايير البحث</p>
            </div>
          )}
        </div>
      </div>

      {/* Order Detail Modal */}
      {isDetailModalOpen && selectedOrder && (
        <div className="responsive-modal">
          <div className="responsive-modal-content max-w-2xl">
            <div className="responsive-modal-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg md:text-2xl font-bold">تفاصيل الطلب #{selectedOrder.id}</h3>
                <button
                  onClick={closeDetailModal}
                  className="text-gray-400 hover:text-white p-2 rounded-xl hover:bg-gray-700/50 transition-colors"
                >
                  <X className="w-5 h-5 md:w-6 md:h-6" />
                </button>
              </div>
            </div>

            <div className="responsive-modal-body">
              {(() => {
                const user = mockUsers.find(u => u.id === selectedOrder.userId)
                const product = mockProducts.find(p => p.id === selectedOrder.productId)
                const package_ = product?.packages.find(p => p.id === selectedOrder.packageId)

                return (
                  <>
                    {/* Order Status */}
                    <div className="flex items-center justify-center mb-6">
                      <div className={`inline-flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-xl text-lg font-semibold ${getStatusColor(selectedOrder.status)}`}>
                        {getStatusIcon(selectedOrder.status)}
                        <span>
                          {selectedOrder.status === "completed" ? "طلب مكتمل" :
                           selectedOrder.status === "pending" ? "قيد الانتظار" : "طلب فاشل"}
                        </span>
                      </div>
                    </div>

                    {/* Order Information Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* User Information */}
                      <div className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                        <h4 className="text-lg font-semibold mb-3 flex items-center space-x-2 space-x-reverse">
                          <User className="w-5 h-5 text-blue-400" />
                          <span>معلومات المستخدم</span>
                        </h4>
                        <div className="space-y-2">
                          <div>
                            <p className="text-sm text-gray-400">الاسم</p>
                            <p className="font-semibold">{user?.name || "مستخدم غير معروف"}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-400">البريد الإلكتروني</p>
                            <p className="font-semibold">{user?.email || "غير متوفر"}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-400">الدور</p>
                            <span className={`px-2 py-1 rounded-lg text-xs ${
                              user?.role === "admin" ? "bg-red-400/10 text-red-400" :
                              user?.role === "distributor" ? "bg-blue-400/10 text-blue-400" :
                              "bg-gray-400/10 text-gray-400"
                            }`}>
                              {user?.role === "admin" ? "مدير" : user?.role === "distributor" ? "موزع" : "مستخدم"}
                            </span>
                          </div>
                          <div>
                            <p className="text-sm text-gray-400">رصيد المحفظة</p>
                            <p className="font-semibold text-green-400">${user?.walletBalance.toFixed(2) || "0.00"}</p>
                          </div>
                        </div>
                      </div>

                      {/* Product Information */}
                      <div className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                        <h4 className="text-lg font-semibold mb-3 flex items-center space-x-2 space-x-reverse">
                          <Package className="w-5 h-5 text-purple-400" />
                          <span>معلومات المنتج</span>
                        </h4>
                        <div className="space-y-2">
                          <div>
                            <p className="text-sm text-gray-400">اسم المنتج</p>
                            <p className="font-semibold">{product?.title || "منتج غير معروف"}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-400">الفئة</p>
                            <p className="font-semibold">{product?.category || "غير محدد"}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-400">الحزمة</p>
                            <p className="font-semibold">{package_?.name || "حزمة غير معروفة"}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-400">سعر الحزمة</p>
                            <p className="font-semibold text-purple-400">{convertAndFormatPrice(package_?.price || 0)}</p>
                          </div>
                          {package_?.hasDigitalCodes && (
                            <div>
                              <p className="text-sm text-gray-400">نوع المنتج</p>
                              <div className="flex items-center space-x-1 space-x-reverse">
                                <Key className="w-4 h-4 text-blue-400" />
                                <span className="text-sm text-blue-400">منتج رقمي</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Order Details */}
                    <div className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                      <h4 className="text-lg font-semibold mb-3 flex items-center space-x-2 space-x-reverse">
                        <DollarSign className="w-5 h-5 text-green-400" />
                        <span>تفاصيل الطلب</span>
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-400">رقم الطلب</p>
                          <p className="font-mono font-semibold">#{selectedOrder.id}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">المبلغ المدفوع</p>
                          <p className="font-bold text-xl text-green-400">{convertAndFormatPrice(selectedOrder.amount)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">تاريخ الطلب</p>
                          <p className="font-semibold">{new Date(selectedOrder.createdAt).toLocaleDateString("ar", {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">طريقة الدفع</p>
                          <p className="font-semibold">محفظة المستخدم</p>
                        </div>
                      </div>
                    </div>

                    {/* Digital Code Section */}
                    {selectedOrder.digitalCode && selectedOrder.status === "completed" && (
                      <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
                        <h4 className="text-lg font-semibold mb-3 flex items-center space-x-2 space-x-reverse">
                          <Key className="w-5 h-5 text-blue-400" />
                          <span className="text-blue-400">الكود الرقمي</span>
                        </h4>
                        <div className="space-y-3">
                          <div className="font-mono text-lg bg-gray-900/50 rounded-lg p-3 border border-gray-600/50">
                            <span className="text-green-400 font-bold">{selectedOrder.digitalCode.key}</span>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-gray-400">حالة الكود</p>
                              <p className="font-semibold text-green-400">مُفعل ومُسلم</p>
                            </div>
                            <div>
                              <p className="text-gray-400">عدد مرات العرض</p>
                              <p className="font-semibold">{selectedOrder.digitalCode.viewCount} مرة</p>
                            </div>
                            {selectedOrder.digitalCode.revealedAt && (
                              <div className="md:col-span-2">
                                <p className="text-gray-400">آخر عرض</p>
                                <p className="font-semibold">{new Date(selectedOrder.digitalCode.revealedAt).toLocaleDateString("ar", {
                                  year: 'numeric',
                                  month: 'long',
                                  day: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Custom Fields if any */}
                    {selectedOrder.customFields && Object.keys(selectedOrder.customFields).length > 0 && (
                      <div className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                        <h4 className="text-lg font-semibold mb-3">معلومات إضافية</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {Object.entries(selectedOrder.customFields).map(([key, value]) => (
                            <div key={key}>
                              <p className="text-sm text-gray-400">{key}</p>
                              <p className="font-semibold">{value}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    {selectedOrder.status === "pending" && (
                      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-600/50">
                        <button
                          onClick={() => {
                            handleOrderAction(selectedOrder.id, "approve")
                            closeDetailModal()
                          }}
                          className="flex-1 flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-xl transition-all duration-300 active:scale-95"
                          disabled={isLoading}
                        >
                          <Check className="w-5 h-5" />
                          <span>موافقة على الطلب</span>
                        </button>
                        <button
                          onClick={() => {
                            handleOrderAction(selectedOrder.id, "decline")
                            closeDetailModal()
                          }}
                          className="flex-1 flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-xl transition-all duration-300 active:scale-95"
                          disabled={isLoading}
                        >
                          <X className="w-5 h-5" />
                          <span>رفض الطلب</span>
                        </button>
                      </div>
                    )}
                  </>
                )
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
