import { 
  validateData, 
  sanitizeString, 
  sanitizeObject,
  userSchema,
  productSchema,
  orderSchema 
} from '../../lib/validations'

describe('Validation Functions', () => {
  describe('validateData', () => {
    it('validates correct user data', () => {
      const validUser = {
        id: '123e4567-e89b-12d3-a456-************',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        role: 'user' as const,
        walletBalance: 100.50,
      }

      const result = validateData(userSchema, validUser)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual(validUser)
      }
    })

    it('rejects invalid user data', () => {
      const invalidUser = {
        id: 'invalid-uuid',
        email: 'invalid-email',
        name: 'A', // Too short
        role: 'invalid-role',
        walletBalance: -10, // Negative balance
      }

      const result = validateData(userSchema, invalidUser)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.errors).toContain('معرف المستخدم يجب أن يكون UUID صالح')
        expect(result.errors).toContain('البريد الإلكتروني غير صالح')
        expect(result.errors).toContain('الاسم يجب أن يكون على الأقل حرفين')
        expect(result.errors).toContain('نوع المستخدم غير صالح')
        expect(result.errors).toContain('رصيد المحفظة لا يمكن أن يكون سالب')
      }
    })

    it('validates product data with packages', () => {
      const validProduct = {
        id: '123e4567-e89b-12d3-a456-************',
        slug: 'test-product',
        title: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        coverImage: 'https://example.com/image.jpg',
        category: 'ألعاب',
        tags: ['تجريبي', 'اختبار'],
        rating: 4.5,
        commentCount: 10,
        packages: [{
          id: '123e4567-e89b-12d3-a456-************',
          name: 'الحزمة الأساسية',
          price: 50.00,
          image: 'https://example.com/package.jpg',
          hasDigitalCodes: false,
        }],
        featured: true,
        popular: false,
      }

      const result = validateData(productSchema, validProduct)
      expect(result.success).toBe(true)
    })

    it('rejects product with invalid slug', () => {
      const invalidProduct = {
        id: '123e4567-e89b-12d3-a456-************',
        slug: 'Invalid Slug With Spaces',
        title: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        coverImage: 'https://example.com/image.jpg',
        category: 'ألعاب',
        packages: [{
          id: '123e4567-e89b-12d3-a456-************',
          name: 'الحزمة الأساسية',
          price: 50.00,
          image: 'https://example.com/package.jpg',
          hasDigitalCodes: false,
        }],
        featured: true,
        popular: false,
      }

      const result = validateData(productSchema, invalidProduct)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.errors).toContain('الرابط المختصر يجب أن يحتوي على أحرف صغيرة وأرقام وشرطات فقط')
      }
    })
  })

  describe('sanitizeString', () => {
    it('removes script tags', () => {
      const maliciousString = '<script>alert("xss")</script>Hello World'
      const sanitized = sanitizeString(maliciousString)
      expect(sanitized).toBe('Hello World')
    })

    it('removes HTML tags', () => {
      const htmlString = '<div><p>Hello <strong>World</strong></p></div>'
      const sanitized = sanitizeString(htmlString)
      expect(sanitized).toBe('Hello World')
    })

    it('removes javascript: protocols', () => {
      const jsString = 'javascript:alert("xss")'
      const sanitized = sanitizeString(jsString)
      expect(sanitized).toBe('alert("xss")')
    })

    it('removes event handlers', () => {
      const eventString = 'onclick="alert(\'xss\')" Hello'
      const sanitized = sanitizeString(eventString)
      expect(sanitized).toBe('"alert(\'xss\')" Hello') // The regex removes 'on\w+\s*=' but keeps the content
    })

    it('trims whitespace', () => {
      const spacedString = '   Hello World   '
      const sanitized = sanitizeString(spacedString)
      expect(sanitized).toBe('Hello World')
    })

    it('preserves safe content', () => {
      const safeString = 'مرحبا بك في المتجر'
      const sanitized = sanitizeString(safeString)
      expect(sanitized).toBe('مرحبا بك في المتجر')
    })
  })

  describe('sanitizeObject', () => {
    it('sanitizes string properties', () => {
      const maliciousObject = {
        name: '<script>alert("xss")</script>أحمد',
        description: '<div>وصف المنتج</div>',
        safe: 'محتوى آمن',
        number: 123,
        boolean: true,
      }

      const sanitized = sanitizeObject(maliciousObject)
      
      expect(sanitized.name).toBe('أحمد')
      expect(sanitized.description).toBe('وصف المنتج')
      expect(sanitized.safe).toBe('محتوى آمن')
      expect(sanitized.number).toBe(123)
      expect(sanitized.boolean).toBe(true)
    })

    it('sanitizes nested objects', () => {
      const nestedObject = {
        user: {
          name: '<script>alert("xss")</script>أحمد',
          profile: {
            bio: '<div>السيرة الذاتية</div>',
          },
        },
        count: 5,
      }

      const sanitized = sanitizeObject(nestedObject)
      
      expect(sanitized.user.name).toBe('أحمد')
      expect(sanitized.user.profile.bio).toBe('السيرة الذاتية')
      expect(sanitized.count).toBe(5)
    })

    it('handles arrays correctly', () => {
      const objectWithArray = {
        tags: ['<script>tag1</script>', 'tag2'],
        numbers: [1, 2, 3],
      }

      const sanitized = sanitizeObject(objectWithArray)
      
      expect(sanitized.tags).toEqual(['<script>tag1</script>', 'tag2']) // Arrays are preserved as-is
      expect(sanitized.numbers).toEqual([1, 2, 3])
    })

    it('handles null and undefined values', () => {
      const objectWithNulls = {
        nullValue: null,
        undefinedValue: undefined,
        emptyString: '',
      }

      const sanitized = sanitizeObject(objectWithNulls)
      
      expect(sanitized.nullValue).toBe(null)
      expect(sanitized.undefinedValue).toBe(undefined)
      expect(sanitized.emptyString).toBe('')
    })
  })
})
