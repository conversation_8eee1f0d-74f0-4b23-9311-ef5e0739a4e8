"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { mockProducts, mockUsers, mockOrders, mockBanners, mockHomepageSections } from "../data/mockData"
import type { Product, User, Order, BannerSlide, HomepageSection } from "../types"

interface DataContextType {
  // Products
  products: Product[]
  setProducts: React.Dispatch<React.SetStateAction<Product[]>>
  updateProduct: (product: Product) => void
  deleteProduct: (productId: string) => void
  addProduct: (product: Product) => void

  // Users
  users: User[]
  setUsers: React.Dispatch<React.SetStateAction<User[]>>
  updateUser: (user: User) => void
  deleteUser: (userId: string) => void
  addUser: (user: User) => void
  currentUser: User | null
  setCurrentUser: React.Dispatch<React.SetStateAction<User | null>>

  // Orders
  orders: Order[]
  setOrders: React.Dispatch<React.SetStateAction<Order[]>>
  updateOrder: (order: Order) => void
  deleteOrder: (orderId: string) => void
  addOrder: (order: Order) => void

  // Homepage Configuration
  banners: BannerSlide[]
  setBanners: React.Dispatch<React.SetStateAction<BannerSlide[]>>
  updateBanner: (banner: BannerSlide) => void
  deleteBanner: (bannerId: string) => void
  addBanner: (banner: BannerSlide) => void

  homepageSections: HomepageSection[]
  setHomepageSections: React.Dispatch<React.SetStateAction<HomepageSection[]>>
  updateHomepageSection: (section: HomepageSection) => void
  deleteHomepageSection: (sectionId: string) => void
  addHomepageSection: (section: HomepageSection) => void

  // Utility functions
  refreshData: () => void
  isLoading: boolean
}

const DataContext = createContext<DataContextType | undefined>(undefined)

export function useData() {
  const context = useContext(DataContext)
  if (context === undefined) {
    throw new Error("useData must be used within a DataProvider")
  }
  return context
}

interface DataProviderProps {
  children: ReactNode
}

export function DataProvider({ children }: DataProviderProps) {
  // State management for all data
  const [products, setProducts] = useState<Product[]>(mockProducts)
  const [users, setUsers] = useState<User[]>(mockUsers)
  const [orders, setOrders] = useState<Order[]>(mockOrders)
  const [banners, setBanners] = useState<BannerSlide[]>(mockBanners)
  const [homepageSections, setHomepageSections] = useState<HomepageSection[]>(mockHomepageSections)
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Initialize current user (mock admin user for now)
  useEffect(() => {
    // TODO: Replace with actual Supabase authentication
    const adminUser = users.find(user => user.role === "admin")
    if (adminUser) {
      setCurrentUser(adminUser)
    }
  }, [users])

  // Product management functions
  const updateProduct = (updatedProduct: Product) => {
    setProducts(prev => prev.map(product => 
      product.id === updatedProduct.id ? updatedProduct : product
    ))
  }

  const deleteProduct = (productId: string) => {
    setProducts(prev => prev.filter(product => product.id !== productId))
  }

  const addProduct = (newProduct: Product) => {
    setProducts(prev => [...prev, newProduct])
  }

  // User management functions
  const updateUser = (updatedUser: User) => {
    setUsers(prev => prev.map(user => 
      user.id === updatedUser.id ? updatedUser : user
    ))
    // Update current user if it's the same user
    if (currentUser && currentUser.id === updatedUser.id) {
      setCurrentUser(updatedUser)
    }
  }

  const deleteUser = (userId: string) => {
    setUsers(prev => prev.filter(user => user.id !== userId))
    // Clear current user if deleted
    if (currentUser && currentUser.id === userId) {
      setCurrentUser(null)
    }
  }

  const addUser = (newUser: User) => {
    setUsers(prev => [...prev, newUser])
  }

  // Order management functions
  const updateOrder = (updatedOrder: Order) => {
    setOrders(prev => prev.map(order => 
      order.id === updatedOrder.id ? updatedOrder : order
    ))
  }

  const deleteOrder = (orderId: string) => {
    setOrders(prev => prev.filter(order => order.id !== orderId))
  }

  const addOrder = (newOrder: Order) => {
    setOrders(prev => [...prev, newOrder])
  }

  // Banner management functions
  const updateBanner = (updatedBanner: BannerSlide) => {
    setBanners(prev => prev.map(banner => 
      banner.id === updatedBanner.id ? updatedBanner : banner
    ))
  }

  const deleteBanner = (bannerId: string) => {
    setBanners(prev => prev.filter(banner => banner.id !== bannerId))
  }

  const addBanner = (newBanner: BannerSlide) => {
    setBanners(prev => [...prev, newBanner])
  }

  // Homepage section management functions
  const updateHomepageSection = (updatedSection: HomepageSection) => {
    setHomepageSections(prev => prev.map(section => 
      section.id === updatedSection.id ? updatedSection : section
    ))
  }

  const deleteHomepageSection = (sectionId: string) => {
    setHomepageSections(prev => prev.filter(section => section.id !== sectionId))
  }

  const addHomepageSection = (newSection: HomepageSection) => {
    setHomepageSections(prev => [...prev, newSection])
  }

  // Utility function to refresh all data
  const refreshData = () => {
    setIsLoading(true)
    // TODO: Implement actual data fetching from Supabase
    setTimeout(() => {
      setProducts(mockProducts)
      setUsers(mockUsers)
      setOrders(mockOrders)
      setBanners(mockBanners)
      setHomepageSections(mockHomepageSections)
      setIsLoading(false)
    }, 1000)
  }

  const value: DataContextType = {
    // Products
    products,
    setProducts,
    updateProduct,
    deleteProduct,
    addProduct,

    // Users
    users,
    setUsers,
    updateUser,
    deleteUser,
    addUser,
    currentUser,
    setCurrentUser,

    // Orders
    orders,
    setOrders,
    updateOrder,
    deleteOrder,
    addOrder,

    // Homepage Configuration
    banners,
    setBanners,
    updateBanner,
    deleteBanner,
    addBanner,

    homepageSections,
    setHomepageSections,
    updateHomepageSection,
    deleteHomepageSection,
    addHomepageSection,

    // Utility
    refreshData,
    isLoading,
  }

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  )
}
