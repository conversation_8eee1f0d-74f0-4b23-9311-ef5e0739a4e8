import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DataProvider, useData } from '../../contexts/DataContext'
import type { Product, User } from '../../types'

// Test component that uses the DataContext
const TestComponent = () => {
  const { 
    products, 
    users, 
    currentUser, 
    addProduct, 
    updateProduct, 
    deleteProduct,
    addUser,
    updateUser,
    deleteUser,
    isLoading,
    error 
  } = useData()

  return (
    <div>
      <div data-testid="products-count">{products.length}</div>
      <div data-testid="users-count">{users.length}</div>
      <div data-testid="current-user">{currentUser?.name || 'No user'}</div>
      <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
      <div data-testid="error">{error || 'No error'}</div>
      
      <button
        onClick={() => addProduct({
          id: '123e4567-e89b-12d3-a456-426614174000',
          slug: 'test-product',
          title: 'Test Product',
          description: 'Test Description for the product',
          coverImage: 'https://example.com/image.jpg',
          category: 'Test',
          tags: [],
          rating: 0,
          commentCount: 0,
          packages: [{
            id: '123e4567-e89b-12d3-a456-426614174001',
            name: 'Test Package',
            price: 10,
            image: 'https://example.com/package.jpg',
            hasDigitalCodes: false,
          }],
          featured: false,
          popular: false,
        } as Product)}
      >
        Add Product
      </button>

      <button
        onClick={() => addUser({
          id: '123e4567-e89b-12d3-a456-426614174002',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'user',
          walletBalance: 0,
        } as User)}
      >
        Add User
      </button>
    </div>
  )
}

describe('DataContext', () => {
  const renderWithProvider = (component: React.ReactElement) => {
    return render(
      <DataProvider>
        {component}
      </DataProvider>
    )
  }

  it('provides initial data', () => {
    renderWithProvider(<TestComponent />)
    
    // Should have initial mock data
    expect(screen.getByTestId('products-count')).toHaveTextContent(/\d+/)
    expect(screen.getByTestId('users-count')).toHaveTextContent(/\d+/)
    expect(screen.getByTestId('loading')).toHaveTextContent('Not loading')
    expect(screen.getByTestId('error')).toHaveTextContent('No error')
  })

  it('adds a product successfully', async () => {
    const user = userEvent.setup()
    renderWithProvider(<TestComponent />)
    
    const initialCount = parseInt(screen.getByTestId('products-count').textContent || '0')
    
    await user.click(screen.getByText('Add Product'))
    
    await waitFor(() => {
      const newCount = parseInt(screen.getByTestId('products-count').textContent || '0')
      expect(newCount).toBe(initialCount + 1)
    })
  })

  it('adds a user successfully', async () => {
    const user = userEvent.setup()
    renderWithProvider(<TestComponent />)
    
    const initialCount = parseInt(screen.getByTestId('users-count').textContent || '0')
    
    await user.click(screen.getByText('Add User'))
    
    await waitFor(() => {
      const newCount = parseInt(screen.getByTestId('users-count').textContent || '0')
      expect(newCount).toBe(initialCount + 1)
    })
  })

  it('handles validation errors', async () => {
    const TestErrorComponent = () => {
      const { addProduct, error } = useData()
      
      return (
        <div>
          <div data-testid="error">{error || 'No error'}</div>
          <button 
            onClick={() => addProduct({
              id: 'invalid-id', // This should be a UUID
              slug: 'Invalid Slug With Spaces', // Invalid slug format
              title: '', // Empty title
              description: '', // Empty description
              coverImage: 'invalid-url', // Invalid URL
              category: '',
              tags: [],
              rating: 0,
              commentCount: 0,
              packages: [], // Empty packages array
              featured: false,
              popular: false,
            } as Product)}
          >
            Add Invalid Product
          </button>
        </div>
      )
    }

    const user = userEvent.setup()
    renderWithProvider(<TestErrorComponent />)
    
    await user.click(screen.getByText('Add Invalid Product'))
    
    await waitFor(() => {
      expect(screen.getByTestId('error')).not.toHaveTextContent('No error')
    })
  })

  it('sets current user from admin users', async () => {
    renderWithProvider(<TestComponent />)
    
    await waitFor(() => {
      // Should eventually set an admin user as current user
      const currentUserText = screen.getByTestId('current-user').textContent
      expect(currentUserText).not.toBe('No user')
    })
  })

  it('throws error when used outside provider', () => {
    // Suppress console.error for this test
    const originalError = console.error
    console.error = jest.fn()
    
    expect(() => {
      render(<TestComponent />)
    }).toThrow('useData must be used within a DataProvider')
    
    console.error = originalError
  })
})
